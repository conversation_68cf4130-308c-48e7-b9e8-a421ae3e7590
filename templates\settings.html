<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ إعدادات متقدمة - Trading Bot</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
        }
        .rule-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        .rule-card.enabled {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .rule-card.disabled {
            border-color: #dc3545;
            background-color: #fff8f8;
            opacity: 0.7;
        }
        .rule-toggle {
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="display-5 mb-0">
                                    <i class="fas fa-cogs text-primary"></i>
                                    الإعدادات المتقدمة
                                </h1>
                                <p class="lead text-muted">تخصيص شامل لنظام التداول</p>
                            </div>
                            <a href="/" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left"></i> العودة للوحة الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Trading Settings -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-coins"></i> إعدادات التداول</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">حجم الصفقة الأساسي</label>
                            <input type="number" class="form-control" id="baseLotSize" 
                                   value="{{ config.trading_settings.base_lot_size }}" step="0.01" min="0.01">
                            <small class="form-text text-muted">الحجم الأساسي لكل صفقة (Lot)</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">أقصى عدد صفقات متزامنة</label>
                            <input type="number" class="form-control" id="maxPositions" 
                                   value="{{ config.trading_settings.max_positions }}" min="1" max="10">
                            <small class="form-text text-muted">العدد الأقصى للصفقات المفتوحة في نفس الوقت</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">نسبة وقف الخسارة (%)</label>
                            <input type="number" class="form-control" id="stopLossPercentage" 
                                   value="{{ (config.trading_settings.stop_loss_percentage * 100)|round(3) }}" 
                                   step="0.1" min="0.1" max="5">
                            <small class="form-text text-muted">نسبة وقف الخسارة من سعر الدخول</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">عدد القواعد المطلوبة للتأكيد</label>
                            <select class="form-select" id="minRulesRequired">
                                <option value="1" {{ 'selected' if config.trading_settings.min_rules_required == 1 }}>
                                    قاعدة واحدة (مخاطرة عالية)
                                </option>
                                <option value="2" {{ 'selected' if config.trading_settings.min_rules_required == 2 }}>
                                    قاعدتان (متوازن - مستحسن)
                                </option>
                                <option value="3" {{ 'selected' if config.trading_settings.min_rules_required == 3 }}>
                                    ثلاث قواعد (محافظ)
                                </option>
                            </select>
                            <small class="form-text text-muted">عدد القواعد التي يجب أن تتفق لفتح صفقة</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الحد الأدنى لمستوى الثقة (%)</label>
                            <input type="range" class="form-range" id="minConfidence" 
                                   value="{{ config.trading_settings.min_confidence }}" min="50" max="95">
                            <div class="d-flex justify-content-between">
                                <small>50%</small>
                                <span id="confidenceValue" class="fw-bold">{{ config.trading_settings.min_confidence }}%</span>
                                <small>95%</small>
                            </div>
                            <small class="form-text text-muted">الحد الأدنى لمستوى الثقة المطلوب لتنفيذ الصفقة</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Simulation Settings -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-bar"></i> إعدادات المحاكاة</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">مدة الاختبار (أيام)</label>
                            <input type="number" class="form-control" id="testDays" 
                                   value="{{ config.simulation_settings.test_days }}" min="1" max="30">
                            <small class="form-text text-muted">عدد الأيام للاختبار الخلفي</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الرصيد الأولي ($)</label>
                            <input type="number" class="form-control" id="initialBalance" 
                                   value="{{ config.simulation_settings.initial_balance }}" min="1000" step="1000">
                            <small class="form-text text-muted">رصيد البداية للمحاكاة</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">السبريد (نقطة)</label>
                            <input type="number" class="form-control" id="spread"
                                   value="{{ config.simulation_settings.spread }}" step="0.1" min="0.1" max="3.0">
                            <div class="mt-2 mb-2">
                                <strong>⚡ إعدادات سريعة:</strong>
                                <button type="button" class="btn btn-sm btn-success me-1" onclick="setSpread(0.4)">عدواني (0.4)</button>
                                <button type="button" class="btn btn-sm btn-warning me-1" onclick="setSpread(0.6)">متوازن (0.6)</button>
                                <button type="button" class="btn btn-sm btn-info me-1" onclick="setSpread(0.9)">محافظ (0.9)</button>
                            </div>
                            <div class="form-text">
                                <strong>💡 توصيات السبريد حسب نوع التداول:</strong><br>
                                <span class="badge bg-success me-1">0.3-0.5</span> تداول عدواني (قاعدة واحدة)<br>
                                <span class="badge bg-warning me-1">0.5-0.8</span> تداول متوازن (قاعدتان)<br>
                                <span class="badge bg-info me-1">0.8-1.0</span> تداول محافظ (3 قواعد)<br>
                                <small class="text-muted">⚠️ السبريد الحالي: 1.5 نقطة (مرتفع جداً - يسبب خسائر كثيرة)</small>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>نصيحة:</strong> ابدأ بمدة قصيرة (3-5 أيام) لاختبار الإعدادات، ثم زد المدة للحصول على نتائج أكثر دقة.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trading Rules -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-rules"></i> قواعد التداول</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p class="mb-2">اختر القواعد التي تريد تفعيلها في النظام:</p>
                                <small class="text-muted">
                                    <span id="enabledCount">{{ config.rule_settings.enabled_rules|length }}</span> 
                                    من {{ all_rules|length if all_rules else 16 }} قاعدة مفعلة
                                </small>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-sm btn-outline-success" onclick="enableAllRules()">
                                    <i class="fas fa-check-double"></i> تفعيل الكل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="disableAllRules()">
                                    <i class="fas fa-times"></i> إلغاء الكل
                                </button>
                            </div>
                        </div>
                        
                        <div class="row" id="rulesContainer">
                            <!-- Rules will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <button class="btn btn-primary btn-lg" onclick="saveAllSettings()">
                    <i class="fas fa-save"></i> حفظ جميع الإعدادات
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Available trading rules
        const allRules = [
            {name: "STRONG_BULL_TREND", label: "اتجاه صاعد قوي", category: "trend"},
            {name: "MOMENTUM_UP", label: "زخم صاعد", category: "momentum"},
            {name: "STRONG_MOMENTUM_UP", label: "زخم صاعد قوي", category: "momentum"},
            {name: "SUPPORT_BOUNCE", label: "ارتداد من الدعم", category: "support_resistance"},
            {name: "BB_BOUNCE_BUY", label: "ارتداد بولينجر للشراء", category: "indicators"},
            {name: "MACD_BULLISH_CROSS", label: "تقاطع MACD صاعد", category: "indicators"},
            {name: "STRONG_BEAR_TREND", label: "اتجاه هابط قوي", category: "trend"},
            {name: "MOMENTUM_DOWN", label: "زخم هابط", category: "momentum"},
            {name: "RESISTANCE_REJECT", label: "رفض من المقاومة", category: "support_resistance"},
            {name: "BB_BOUNCE_SELL", label: "ارتداد بولينجر للبيع", category: "indicators"},
            {name: "MACD_BEARISH_CROSS", label: "تقاطع MACD هابط", category: "indicators"},
            {name: "BREAKOUT_UP", label: "كسر صاعد", category: "breakout"},
            {name: "BREAKOUT_DOWN", label: "كسر هابط", category: "breakout"},
            {name: "RSI_OVERSOLD", label: "RSI تشبع بيعي", category: "indicators"},
            {name: "RSI_OVERBOUGHT", label: "RSI تشبع شرائي", category: "indicators"}
        ];

        const enabledRules = {{ config.rule_settings.enabled_rules | tojson }};

        // Update confidence value display
        document.getElementById('minConfidence').addEventListener('input', function() {
            document.getElementById('confidenceValue').textContent = this.value + '%';
        });

        // Populate rules
        function populateRules() {
            const container = document.getElementById('rulesContainer');
            container.innerHTML = '';

            allRules.forEach(rule => {
                const isEnabled = enabledRules.includes(rule.name);
                const categoryIcon = getCategoryIcon(rule.category);
                
                const ruleHtml = `
                    <div class="col-md-6 col-lg-4 mb-2">
                        <div class="rule-card ${isEnabled ? 'enabled' : 'disabled'}" id="rule-${rule.name}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas ${categoryIcon} text-muted"></i>
                                    <span class="ms-2">${rule.label}</span>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input rule-toggle" type="checkbox" 
                                           id="toggle-${rule.name}" ${isEnabled ? 'checked' : ''}
                                           onchange="toggleRule('${rule.name}')">
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += ruleHtml;
            });

            updateEnabledCount();
        }

        function getCategoryIcon(category) {
            const icons = {
                'trend': 'fa-chart-line',
                'momentum': 'fa-tachometer-alt',
                'support_resistance': 'fa-layer-group',
                'indicators': 'fa-chart-area',
                'breakout': 'fa-expand-arrows-alt'
            };
            return icons[category] || 'fa-cog';
        }

        function toggleRule(ruleName) {
            const checkbox = document.getElementById(`toggle-${ruleName}`);
            const ruleCard = document.getElementById(`rule-${ruleName}`);
            
            if (checkbox.checked) {
                ruleCard.className = 'rule-card enabled';
                if (!enabledRules.includes(ruleName)) {
                    enabledRules.push(ruleName);
                }
            } else {
                ruleCard.className = 'rule-card disabled';
                const index = enabledRules.indexOf(ruleName);
                if (index > -1) {
                    enabledRules.splice(index, 1);
                }
            }
            
            updateEnabledCount();
        }

        function enableAllRules() {
            allRules.forEach(rule => {
                const checkbox = document.getElementById(`toggle-${rule.name}`);
                const ruleCard = document.getElementById(`rule-${rule.name}`);
                checkbox.checked = true;
                ruleCard.className = 'rule-card enabled';
                if (!enabledRules.includes(rule.name)) {
                    enabledRules.push(rule.name);
                }
            });
            updateEnabledCount();
        }

        function disableAllRules() {
            allRules.forEach(rule => {
                const checkbox = document.getElementById(`toggle-${rule.name}`);
                const ruleCard = document.getElementById(`rule-${rule.name}`);
                checkbox.checked = false;
                ruleCard.className = 'rule-card disabled';
                const index = enabledRules.indexOf(rule.name);
                if (index > -1) {
                    enabledRules.splice(index, 1);
                }
            });
            updateEnabledCount();
        }

        function updateEnabledCount() {
            document.getElementById('enabledCount').textContent = enabledRules.length;
        }

        function saveAllSettings() {
            const settings = {
                trading_settings: {
                    base_lot_size: parseFloat(document.getElementById('baseLotSize').value),
                    max_positions: parseInt(document.getElementById('maxPositions').value),
                    stop_loss_percentage: parseFloat(document.getElementById('stopLossPercentage').value) / 100,
                    min_rules_required: parseInt(document.getElementById('minRulesRequired').value),
                    min_confidence: parseInt(document.getElementById('minConfidence').value)
                },
                simulation_settings: {
                    test_days: parseInt(document.getElementById('testDays').value),
                    initial_balance: parseInt(document.getElementById('initialBalance').value),
                    spread: parseFloat(document.getElementById('spread').value)
                },
                rule_settings: {
                    enabled_rules: enabledRules
                }
            };

            fetch('/api/update_settings', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ تم حفظ الإعدادات بنجاح!');
                } else {
                    alert('❌ خطأ في حفظ الإعدادات: ' + data.error);
                }
            })
            .catch(error => {
                alert('❌ خطأ في الاتصال: ' + error);
            });
        }

        // Quick spread setting function
        function setSpread(value) {
            document.getElementById('spread').value = value;
            // Show immediate feedback
            const spreadInput = document.getElementById('spread');
            spreadInput.style.backgroundColor = '#d4edda';
            setTimeout(() => {
                spreadInput.style.backgroundColor = '';
            }, 1000);
        }

        // Initialize
        populateRules();
    </script>
</body>
</html>
