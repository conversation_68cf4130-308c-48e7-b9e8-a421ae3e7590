#!/usr/bin/env python3
# test_consistency.py
# اختبار ثبات نتائج المحاكاة

import json
from datetime import datetime, timedelta
from simulation import AdvancedTradingSimulation
from mt5_interface import initialize_mt5_connection, shutdown_mt5_connection, get_historical_data
from config import SYMB<PERSON>, TIMEFRAME_TO_USE

def test_simulation_consistency(runs=3):
    """اختبار ثبات المحاكاة عبر عدة تشغيلات"""
    
    print(f"🧪 اختبار ثبات المحاكاة ({runs} تشغيلات)")
    print("=" * 60)
    
    # إعدادات ثابتة للاختبار
    test_config = {
        'trading_settings': {
            'base_lot_size': 0.01,
            'max_positions': 2,
            'stop_loss_percentage': 0.015,
            'min_rules_required': 2,
            'min_confidence': 80
        },
        'simulation_settings': {
            'test_days': 5,
            'initial_balance': 10000,
            'spread': 0.3
        },
        'rule_settings': {
            'enabled_rules': [
                "STRONG_BULL_TREND", "MOMENTUM_UP", "STRONG_MOMENTUM_UP",
                "SUPPORT_BOUNCE", "BB_BOUNCE_BUY", "MACD_BULLISH_CROSS",
                "STRONG_BEAR_TREND", "MOMENTUM_DOWN",
                "RESISTANCE_REJECT", "BB_BOUNCE_SELL", "MACD_BEARISH_CROSS",
                "BREAKOUT_UP", "BREAKOUT_DOWN", "RSI_OVERSOLD", "RSI_OVERBOUGHT"
            ]
        },
        'protection_settings': {
            'min_trade_duration_minutes': 30,
            'min_profit_target_pips': 7.0,
            'opposite_signal_confidence_threshold': 90,
            'min_trade_age_for_opposite_close': 30,
            'min_loss_for_opposite_close': 8.0
        }
    }
    
    # تواريخ ثابتة للاختبار (استخدام وقت حديث)
    now = datetime.now()
    end_date = now.replace(minute=0, second=0, microsecond=0)

    # إذا كان نهاية أسبوع، ارجع للجمعة
    if end_date.weekday() >= 5:  # Saturday=5, Sunday=6
        days_to_friday = end_date.weekday() - 4  # Friday=4
        end_date = end_date - timedelta(days=days_to_friday)

    test_days = test_config['simulation_settings']['test_days']
    extra_days = (test_days // 5) * 2
    total_days = test_days + extra_days
    start_date = end_date - timedelta(days=total_days)
    
    print(f"📅 فترة الاختبار الثابتة:")
    print(f"   من: {start_date.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   إلى: {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   المدة: {test_days} أيام تداول")
    
    if not initialize_mt5_connection():
        print("❌ فشل الاتصال بـ MT5")
        return
    
    results = []
    
    try:
        for run in range(1, runs + 1):
            print(f"\n🔄 التشغيل {run}/{runs}")
            print("-" * 30)
            
            # إنشاء محاكاة جديدة
            sim = AdvancedTradingSimulation()
            
            # تطبيق الإعدادات
            sim.base_lot_size = test_config['trading_settings']['base_lot_size']
            sim.max_positions = test_config['trading_settings']['max_positions']
            sim.stop_loss_percentage = test_config['trading_settings']['stop_loss_percentage']
            sim.min_rules_required = test_config['trading_settings']['min_rules_required']
            sim.min_confidence = test_config['trading_settings']['min_confidence']
            sim.enabled_rules = test_config['rule_settings']['enabled_rules']
            sim.spread = test_config['simulation_settings']['spread']
            
            # تشغيل المحاكاة
            success = sim.run_individual_signals_simulation(start_date, end_date)
            
            if success:
                result = {
                    'run': run,
                    'total_trades': len(sim.trades),
                    'winning_trades': len([t for t in sim.trades if t['profit'] > 0]),
                    'losing_trades': len([t for t in sim.trades if t['profit'] <= 0]),
                    'final_balance': sim.balance,
                    'profit_loss': sim.balance - sim.initial_balance,
                    'profit_percentage': ((sim.balance - sim.initial_balance) / sim.initial_balance) * 100,
                    'first_trade_time': sim.trades[0]['entry_time'].strftime('%Y-%m-%d %H:%M:%S') if sim.trades else None,
                    'last_trade_time': sim.trades[-1]['exit_time'].strftime('%Y-%m-%d %H:%M:%S') if sim.trades else None,
                    'decision_count': len(sim.decision_log) if hasattr(sim, 'decision_log') else 0
                }
                
                results.append(result)
                
                print(f"   ✅ صفقات: {result['total_trades']}")
                print(f"   💰 الربح/الخسارة: ${result['profit_loss']:.2f} ({result['profit_percentage']:.2f}%)")
                print(f"   📊 رابحة/خاسرة: {result['winning_trades']}/{result['losing_trades']}")
                print(f"   🧠 قرارات: {result['decision_count']}")
                
            else:
                print(f"   ❌ فشل التشغيل {run}")
    
    finally:
        shutdown_mt5_connection()
    
    # تحليل النتائج
    print(f"\n📊 تحليل الثبات:")
    print("=" * 40)
    
    if len(results) < 2:
        print("❌ لا توجد نتائج كافية للمقارنة")
        return
    
    # فحص الثبات
    first_result = results[0]
    consistent = True
    
    for i, result in enumerate(results[1:], 2):
        print(f"\n🔍 مقارنة التشغيل 1 مع التشغيل {i}:")
        
        # مقارنة النتائج الأساسية
        if result['total_trades'] != first_result['total_trades']:
            print(f"   ⚠️ عدد الصفقات مختلف: {first_result['total_trades']} vs {result['total_trades']}")
            consistent = False
        else:
            print(f"   ✅ عدد الصفقات متطابق: {result['total_trades']}")
        
        if abs(result['profit_loss'] - first_result['profit_loss']) > 0.01:
            print(f"   ⚠️ الربح/الخسارة مختلف: ${first_result['profit_loss']:.2f} vs ${result['profit_loss']:.2f}")
            consistent = False
        else:
            print(f"   ✅ الربح/الخسارة متطابق: ${result['profit_loss']:.2f}")
        
        if result['first_trade_time'] != first_result['first_trade_time']:
            print(f"   ⚠️ وقت أول صفقة مختلف: {first_result['first_trade_time']} vs {result['first_trade_time']}")
            consistent = False
        else:
            print(f"   ✅ وقت أول صفقة متطابق: {result['first_trade_time']}")
    
    # النتيجة النهائية
    print(f"\n🎯 النتيجة النهائية:")
    if consistent:
        print("✅ المحاكاة ثابتة - نفس النتائج في جميع التشغيلات")
    else:
        print("❌ المحاكاة غير ثابتة - نتائج مختلفة بين التشغيلات")
        print("\n💡 الأسباب المحتملة:")
        print("   • تواريخ متغيرة")
        print("   • ترتيب بيانات غير ثابت")
        print("   • عشوائية في المنطق")
        print("   • مشاكل في البيانات")
    
    # حفظ النتائج
    with open('consistency_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ النتائج في: consistency_test_results.json")

    return results, consistent

def main():
    try:
        results, is_consistent = test_simulation_consistency(3)

        if is_consistent:
            print("\n🎉 المحاكاة تعمل بثبات!")
        else:
            print("\n⚠️ المحاكاة تحتاج إصلاح لضمان الثبات")

    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        return [], False

if __name__ == "__main__":
    main()
