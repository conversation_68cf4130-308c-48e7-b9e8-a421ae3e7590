#!/usr/bin/env python3
# debug_simulation.py
# تشخيص مشاكل المحاكاة

from datetime import datetime, timedelta
import json
from simulation import AdvancedTradingSimulation
from mt5_interface import initialize_mt5_connection, shutdown_mt5_connection, get_historical_data
from config import SYMBOL, TIMEFRAME_TO_USE

def debug_simulation_data(test_days):
    """تشخيص بيانات المحاكاة لفهم المشكلة"""
    
    print(f"\n🔍 تشخيص المحاكاة لـ {test_days} أيام")
    print("=" * 60)
    
    # حساب التواريخ
    end_date = datetime.now()
    start_date = end_date - timedelta(days=test_days)
    
    print(f"📅 فترة المحاكاة:")
    print(f"   من: {start_date.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   إلى: {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   المدة: {test_days} أيام")
    
    # الاتصال بـ MT5
    if not initialize_mt5_connection():
        print("❌ فشل الاتصال بـ MT5")
        return
    
    try:
        # جلب البيانات
        total_candles = 2000
        rates = get_historical_data(SYMBOL, TIMEFRAME_TO_USE, total_candles)
        
        if rates is None or rates.empty:
            print("❌ فشل في جلب البيانات التاريخية")
            return
        
        print(f"\n📊 البيانات المجلبة:")
        print(f"   إجمالي الشموع: {len(rates)}")
        print(f"   أقدم شمعة: {rates['time'].iloc[0]}")
        print(f"   أحدث شمعة: {rates['time'].iloc[-1]}")
        
        # تصفية البيانات حسب التاريخ
        import pandas as pd
        rates['time'] = pd.to_datetime(rates['time'])
        mask = (rates['time'] >= start_date) & (rates['time'] <= end_date)
        simulation_data = rates.loc[mask].copy()
        
        print(f"\n🎯 البيانات المفلترة:")
        print(f"   شموع في الفترة المطلوبة: {len(simulation_data)}")
        
        if len(simulation_data) > 0:
            print(f"   أول شمعة: {simulation_data['time'].iloc[0]}")
            print(f"   آخر شمعة: {simulation_data['time'].iloc[-1]}")
            
            # تحليل توزيع البيانات
            days_with_data = simulation_data['time'].dt.date.nunique()
            avg_candles_per_day = len(simulation_data) / days_with_data if days_with_data > 0 else 0
            
            print(f"   أيام تحتوي على بيانات: {days_with_data}")
            print(f"   متوسط الشموع/اليوم: {avg_candles_per_day:.1f}")
            
            # تحقق من نهايات الأسبوع
            weekdays = simulation_data['time'].dt.dayofweek.value_counts().sort_index()
            print(f"\n📈 توزيع البيانات حسب أيام الأسبوع:")
            days_names = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
            for day, count in weekdays.items():
                print(f"   {days_names[day]}: {count} شمعة")
        else:
            print("   ❌ لا توجد بيانات في الفترة المطلوبة!")
            
            # تحقق من سبب عدم وجود البيانات
            print(f"\n🔍 تشخيص المشكلة:")
            latest_data = rates['time'].max()
            oldest_data = rates['time'].min()
            
            if start_date < oldest_data:
                print(f"   ⚠️ تاريخ البداية ({start_date}) أقدم من أقدم بيانات متاحة ({oldest_data})")
            
            if end_date > latest_data:
                print(f"   ⚠️ تاريخ النهاية ({end_date}) أحدث من أحدث بيانات متاحة ({latest_data})")
                
            # اقتراح فترة صالحة
            available_days = (latest_data - oldest_data).days
            print(f"   💡 الفترة المتاحة: {available_days} يوم")
            print(f"   💡 اقتراح: استخدم فترة أقل من {available_days} أيام")
    
    finally:
        shutdown_mt5_connection()

def test_different_periods():
    """اختبار فترات مختلفة لفهم المشكلة"""
    
    print("\n🧪 اختبار فترات مختلفة:")
    print("=" * 60)
    
    periods = [1, 3, 5, 7, 10, 15, 30]
    
    for days in periods:
        debug_simulation_data(days)
        print("\n" + "-" * 40)

if __name__ == "__main__":
    # تشخيص المشكلة
    test_different_periods()
    
    print("\n🎯 خلاصة المشكلة:")
    print("1. تحقق من توفر البيانات في الفترة المطلوبة")
    print("2. تجنب نهايات الأسبوع (السبت والأحد)")
    print("3. استخدم فترات ضمن البيانات المتاحة")
    print("4. تأكد من أن MT5 متصل ويحتوي على بيانات حديثة")
