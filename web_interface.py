#!/usr/bin/env python3
# web_interface.py
# Web interface for trading bot control

from flask import Flask, render_template, request, jsonify
import json
import os
from datetime import datetime, timedelta
import threading

# Safe pandas import
try:
    import pandas as pd
except ImportError:
    pd = None

from simulation import AdvancedTradingSimulation

app = Flask(__name__)

# Global variables for simulation control
simulation_running = False
simulation_results = None
simulation_thread = None

def convert_trades_to_json(trades):
    """Convert trades list to JSON-serializable format"""
    json_trades = []
    for trade in trades:
        json_trade = {}
        for key, value in trade.items():
            if pd and hasattr(pd, 'Timedelta') and isinstance(value, pd.Timedelta):
                # Convert Timedelta to total seconds
                json_trade[key] = value.total_seconds()
            elif pd and hasattr(pd, 'Timestamp') and isinstance(value, pd.Timestamp):
                # Convert pandas Timestamp to string
                json_trade[key] = value.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(value, datetime):
                # Convert datetime to string
                json_trade[key] = value.strftime('%Y-%m-%d %H:%M:%S')
            elif pd and hasattr(pd, 'isna') and pd.isna(value):
                # Convert NaN to None
                json_trade[key] = None
            elif value is None:
                # Handle None values
                json_trade[key] = None
            elif str(value).lower() in ['nan', 'nat']:
                # Handle string representations of NaN
                json_trade[key] = None
            else:
                json_trade[key] = value
        json_trades.append(json_trade)
    return json_trades

def analyze_losses(trades):
    """Analyze losing trades to identify patterns and causes"""
    if not trades:
        return {}

    losing_trades = [t for t in trades if t['profit'] <= 0]
    winning_trades = [t for t in trades if t['profit'] > 0]

    if not losing_trades:
        return {'message': 'لا توجد صفقات خاسرة للتحليل'}

    analysis = {
        'total_trades': len(trades),
        'losing_trades_count': len(losing_trades),
        'winning_trades_count': len(winning_trades),
        'loss_rate': (len(losing_trades) / len(trades)) * 100,
        'total_loss': sum([t['profit'] for t in losing_trades]),
        'avg_loss': sum([t['profit'] for t in losing_trades]) / len(losing_trades),
        'biggest_loss': min([t['profit'] for t in losing_trades]),
        'smallest_loss': max([t['profit'] for t in losing_trades]),
    }

    # Analyze loss causes
    loss_causes = {}
    spread_losses = 0
    stop_loss_hits = 0
    opposite_signal_closes = 0
    end_simulation_closes = 0

    for trade in losing_trades:
        reason = trade.get('reason', 'Unknown')
        loss_causes[reason] = loss_causes.get(reason, 0) + 1

        # Count specific causes
        if 'Stop Loss' in reason:
            stop_loss_hits += 1
        elif 'Opposite Signal' in reason:
            opposite_signal_closes += 1
        elif 'End of Simulation' in reason:
            end_simulation_closes += 1

        # Calculate spread impact (approximate)
        if abs(trade['profit']) < 2.0:  # Small losses likely due to spread
            spread_losses += 1

    analysis['loss_causes'] = loss_causes
    analysis['spread_impact'] = {
        'spread_related_losses': spread_losses,
        'spread_loss_percentage': (spread_losses / len(losing_trades)) * 100
    }

    # Analyze by confidence levels
    high_conf_losses = [t for t in losing_trades if t['confidence'] >= 80]
    medium_conf_losses = [t for t in losing_trades if 60 <= t['confidence'] < 80]
    low_conf_losses = [t for t in losing_trades if t['confidence'] < 60]

    analysis['confidence_analysis'] = {
        'high_confidence_losses': len(high_conf_losses),
        'medium_confidence_losses': len(medium_conf_losses),
        'low_confidence_losses': len(low_conf_losses),
        'high_conf_loss_rate': (len(high_conf_losses) / len(losing_trades)) * 100 if losing_trades else 0
    }

    # Analyze by trade duration
    short_trades = [t for t in losing_trades if hasattr(t.get('duration'), 'total_seconds') and t['duration'].total_seconds() < 3600]  # < 1 hour
    medium_trades = [t for t in losing_trades if hasattr(t.get('duration'), 'total_seconds') and 3600 <= t['duration'].total_seconds() < 14400]  # 1-4 hours
    long_trades = [t for t in losing_trades if hasattr(t.get('duration'), 'total_seconds') and t['duration'].total_seconds() >= 14400]  # > 4 hours

    analysis['duration_analysis'] = {
        'short_duration_losses': len(short_trades),
        'medium_duration_losses': len(medium_trades),
        'long_duration_losses': len(long_trades)
    }

    # Generate recommendations
    recommendations = []

    if analysis['spread_impact']['spread_loss_percentage'] > 50:
        recommendations.append("🔴 أكثر من 50% من الخسائر بسبب السبريد - قلل السبريد إلى 0.3-0.5 نقطة")

    if stop_loss_hits > len(losing_trades) * 0.3:
        recommendations.append("🔴 أكثر من 30% من الخسائر بسبب وقف الخسارة - زد نسبة وقف الخسارة أو ألغها")

    if analysis['confidence_analysis']['high_conf_loss_rate'] > 40:
        recommendations.append("🔴 خسائر كثيرة في الصفقات عالية الثقة - راجع قواعد التداول")

    if len(short_trades) > len(losing_trades) * 0.4:
        recommendations.append("🔴 أكثر من 40% من الخسائر في صفقات قصيرة المدى - زد فترة التأكيد")

    if analysis['loss_rate'] > 80:
        recommendations.append("🔴 معدل خسارة عالي جداً - غير الاستراتيجية أو قلل عدد الصفقات")

    if not recommendations:
        recommendations.append("✅ لا توجد مشاكل واضحة - النتائج ضمن المعدل الطبيعي")

    analysis['recommendations'] = recommendations

    return analysis

class WebConfig:
    """Configuration manager for web interface"""
    
    def __init__(self):
        self.config_file = "web_config.json"
        self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        default_config = {
            "trading_settings": {
                "base_lot_size": 0.02,
                "max_positions": 3,
                "stop_loss_percentage": 0.005,
                "min_rules_required": 2,
                "min_confidence": 70
            },
            "simulation_settings": {
                "test_days": 3,
                "initial_balance": 10000,
                "spread": 0.5
            },
            "rule_settings": {
                "enabled_rules": [
                    "STRONG_BULL_TREND", "MOMENTUM_UP", "STRONG_MOMENTUM_UP",
                    "SUPPORT_BOUNCE", "BB_BOUNCE_BUY", "MACD_BULLISH_CROSS",
                    "STRONG_BEAR_TREND", "MOMENTUM_DOWN",
                    "RESISTANCE_REJECT", "BB_BOUNCE_SELL", "MACD_BEARISH_CROSS",
                    "BREAKOUT_UP", "BREAKOUT_DOWN", "RSI_OVERSOLD", "RSI_OVERBOUGHT"
                ]
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except:
                self.config = default_config
        else:
            self.config = default_config
        
        self.save_config()
    
    def save_config(self):
        """Save configuration to file"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def get(self, section, key):
        """Get configuration value"""
        return self.config.get(section, {}).get(key)
    
    def set(self, section, key, value):
        """Set configuration value"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
        self.save_config()

# Initialize configuration
web_config = WebConfig()

def run_simulation_background(config):
    """Run simulation in background thread"""
    global simulation_running, simulation_results

    try:
        simulation_running = True

        # Create simulation instance
        sim = AdvancedTradingSimulation(
            initial_balance=config['simulation_settings']['initial_balance'],
            base_lot_size=config['trading_settings']['base_lot_size'],
            spread=config['simulation_settings']['spread'],
            use_stop_loss=True
        )

        # Update protection settings from config
        sim.update_protection_settings(config)

        # Set parameters
        sim.max_positions = config['trading_settings']['max_positions']
        sim.stop_loss_percentage = config['trading_settings']['stop_loss_percentage']

        # Apply configuration to simulation
        sim.min_rules_required = config['trading_settings']['min_rules_required']
        sim.min_confidence = config['trading_settings']['min_confidence']
        sim.enabled_rules = config['rule_settings']['enabled_rules']

        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=config['simulation_settings']['test_days'])

        # Run simulation
        success = sim.run_individual_signals_simulation(start_date, end_date)
        
        if success:
            # Get results
            results = sim.print_advanced_results()
            
            # Add additional analysis
            simulation_results = {
                'success': True,
                'basic_stats': results,
                'detailed_stats': {
                    'total_trades': len(sim.trades),
                    'winning_trades': len([t for t in sim.trades if t['profit'] > 0]),
                    'losing_trades': len([t for t in sim.trades if t['profit'] <= 0]),
                    'best_trade': max([t['profit'] for t in sim.trades]) if sim.trades else 0,
                    'worst_trade': min([t['profit'] for t in sim.trades]) if sim.trades else 0,
                    'daily_trades': len(sim.trades) / config['simulation_settings']['test_days'],
                    'balance_change': sim.balance - sim.initial_balance,
                    'balance_change_pct': ((sim.balance - sim.initial_balance) / sim.initial_balance) * 100
                },
                'trades': convert_trades_to_json(sim.trades[-10:] if sim.trades else []),  # Last 10 trades
                'loss_analysis': analyze_losses(sim.trades) if sim.trades else {},  # Loss analysis
                'config_used': config,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        else:
            simulation_results = {
                'success': False,
                'error': 'Simulation failed to run',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    except Exception as e:
        simulation_results = {
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    finally:
        simulation_running = False

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html', 
                         config=web_config.config,
                         simulation_running=simulation_running,
                         simulation_results=simulation_results)

@app.route('/settings')
def settings():
    """Settings page"""
    return render_template('settings.html', config=web_config.config)

@app.route('/api/update_settings', methods=['POST'])
def update_settings():
    """Update settings via API"""
    try:
        data = request.json
        
        # Update trading settings
        if 'trading_settings' in data:
            for key, value in data['trading_settings'].items():
                web_config.set('trading_settings', key, value)
        
        # Update simulation settings
        if 'simulation_settings' in data:
            for key, value in data['simulation_settings'].items():
                web_config.set('simulation_settings', key, value)
        
        # Update rule settings
        if 'rule_settings' in data:
            for key, value in data['rule_settings'].items():
                web_config.set('rule_settings', key, value)
        
        return jsonify({'success': True, 'message': 'Settings updated successfully'})
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/run_simulation', methods=['POST'])
def run_simulation():
    """Start simulation"""
    global simulation_thread, simulation_running
    
    if simulation_running:
        return jsonify({'success': False, 'error': 'Simulation already running'})
    
    try:
        # Load the latest configuration from file
        web_config.load_config()
        current_config = web_config.config.copy()

        print(f"🔧 Using configuration:")
        print(f"   • Spread: {current_config['simulation_settings']['spread']} points")
        print(f"   • Min rules: {current_config['trading_settings']['min_rules_required']}")
        print(f"   • Min confidence: {current_config['trading_settings']['min_confidence']}%")
        print(f"   • Test days: {current_config['simulation_settings']['test_days']}")

        # Start simulation in background thread
        simulation_thread = threading.Thread(
            target=run_simulation_background,
            args=(current_config,)
        )
        simulation_thread.start()

        return jsonify({'success': True, 'message': 'Simulation started'})
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/simulation_status')
def simulation_status():
    """Get simulation status"""
    return jsonify({
        'running': simulation_running,
        'results': simulation_results
    })

@app.route('/api/stop_simulation', methods=['POST'])
def stop_simulation():
    """Stop simulation"""
    global simulation_running
    simulation_running = False
    return jsonify({'success': True, 'message': 'Simulation stop requested'})

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("🌐 Starting Trading Bot Web Interface...")
    print("📱 Access the interface at: http://localhost:5000")
    print("🔧 Settings page: http://localhost:5000/settings")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
