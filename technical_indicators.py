# technical_indicators.py
# Advanced technical indicators for improved trading strategy

import pandas as pd
import numpy as np

try:
    from .config import (
        EMA_FAST, EMA_SLOW, EMA_TREND,
        RSI_PERIOD, RSI_OVERBOUGHT, RSI_OVERSOLD, RSI_NEUTRAL_HIGH, RSI_NEUTRAL_LOW,
        MACD_FAST, MACD_SLOW, MACD_SIGNAL,
        BB_PERIOD, BB_STD,
        VOLUME_MA_PERIOD, VOLUME_SPIKE_RATIO
    )
except ImportError:
    from config import (
        EMA_FAST, EMA_SLOW, EMA_TREND,
        RSI_PERIOD, RSI_OVERBOUGHT, RSI_OVERSOLD, RSI_NEUTRAL_HIGH, RSI_NEUTRAL_LOW,
        MACD_FAST, MACD_SLOW, MACD_SIGNAL,
        BB_PERIOD, BB_STD,
        VOLUME_MA_PERIOD, VOLUME_SPIKE_RATIO
    )


def calculate_ema(data, period):
    """
    Calculate Exponential Moving Average
    """
    return data.ewm(span=period, adjust=False).mean()


def calculate_rsi(data, period=RSI_PERIOD):
    """
    Calculate Relative Strength Index
    """
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi


def calculate_macd(data, fast=MACD_FAST, slow=MACD_SLOW, signal=MACD_SIGNAL):
    """
    Calculate MACD (Moving Average Convergence Divergence)
    """
    ema_fast = calculate_ema(data, fast)
    ema_slow = calculate_ema(data, slow)
    macd_line = ema_fast - ema_slow
    signal_line = calculate_ema(macd_line, signal)
    histogram = macd_line - signal_line
    
    return {
        'macd': macd_line,
        'signal': signal_line,
        'histogram': histogram
    }


def calculate_bollinger_bands(data, period=BB_PERIOD, std_dev=BB_STD):
    """
    Calculate Bollinger Bands
    """
    sma = data.rolling(window=period).mean()
    std = data.rolling(window=period).std()
    
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    
    return {
        'upper': upper_band,
        'middle': sma,
        'lower': lower_band
    }


def calculate_volume_analysis(volume_data, period=VOLUME_MA_PERIOD):
    """
    Calculate volume analysis indicators
    """
    volume_ma = volume_data.rolling(window=period).mean()
    volume_ratio = volume_data / volume_ma
    
    return {
        'volume_ma': volume_ma,
        'volume_ratio': volume_ratio,
        'is_high_volume': volume_ratio > VOLUME_SPIKE_RATIO
    }


def get_all_indicators(rates):
    """
    Calculate all technical indicators for the given rates
    """
    if rates is None or rates.empty or len(rates) < max(EMA_TREND, BB_PERIOD, RSI_PERIOD):
        print("Insufficient data for indicator calculation")
        return None
    
    close_prices = rates['close']
    high_prices = rates['high']
    low_prices = rates['low']
    volume_data = rates['tick_volume'] if 'tick_volume' in rates.columns else None
    
    # Calculate all indicators
    indicators = {
        # EMAs
        'ema_fast': calculate_ema(close_prices, EMA_FAST),
        'ema_slow': calculate_ema(close_prices, EMA_SLOW),
        'ema_trend': calculate_ema(close_prices, EMA_TREND),
        
        # RSI
        'rsi': calculate_rsi(close_prices),
        
        # MACD
        'macd_data': calculate_macd(close_prices),
        
        # Bollinger Bands
        'bb_data': calculate_bollinger_bands(close_prices),
        
        # Price action
        'current_price': close_prices.iloc[-1],
        'previous_price': close_prices.iloc[-2] if len(close_prices) > 1 else close_prices.iloc[-1],
    }
    
    # Volume analysis (if available)
    if volume_data is not None:
        indicators['volume_data'] = calculate_volume_analysis(volume_data)
    
    return indicators


def analyze_trend_strength(indicators):
    """
    Analyze overall trend strength using multiple indicators
    """
    if not indicators:
        return "UNKNOWN"
    
    ema_fast = indicators['ema_fast'].iloc[-1]
    ema_slow = indicators['ema_slow'].iloc[-1]
    ema_trend = indicators['ema_trend'].iloc[-1]
    current_price = indicators['current_price']
    rsi = indicators['rsi'].iloc[-1]
    macd_data = indicators['macd_data']
    
    # EMA trend analysis
    ema_bullish = ema_fast > ema_slow > ema_trend
    ema_bearish = ema_fast < ema_slow < ema_trend
    
    # MACD analysis
    macd_bullish = (macd_data['macd'].iloc[-1] > macd_data['signal'].iloc[-1] and 
                   macd_data['histogram'].iloc[-1] > 0)
    macd_bearish = (macd_data['macd'].iloc[-1] < macd_data['signal'].iloc[-1] and 
                   macd_data['histogram'].iloc[-1] < 0)
    
    # Price position relative to EMAs
    price_above_trend = current_price > ema_trend
    price_below_trend = current_price < ema_trend
    
    # Count bullish/bearish signals
    bullish_signals = sum([ema_bullish, macd_bullish, price_above_trend, rsi > 50])
    bearish_signals = sum([ema_bearish, macd_bearish, price_below_trend, rsi < 50])
    
    if bullish_signals >= 3:
        return "STRONG_BULLISH"
    elif bullish_signals >= 2:
        return "BULLISH"
    elif bearish_signals >= 3:
        return "STRONG_BEARISH"
    elif bearish_signals >= 2:
        return "BEARISH"
    else:
        return "NEUTRAL"


def get_entry_signals(indicators, resistance_level, support_level):
    """
    Generate entry signals based on multiple indicators
    """
    if not indicators:
        return "NO_SIGNAL", 0  # signal, confidence
    
    current_price = indicators['current_price']
    rsi = indicators['rsi'].iloc[-1]
    macd_data = indicators['macd_data']
    bb_data = indicators['bb_data']
    trend_strength = analyze_trend_strength(indicators)
    
    # Initialize individual signal tracking (execute on ANY signal)
    individual_signals = []
    buy_signals = 0
    sell_signals = 0
    max_signals = 6
    
    # 1. RSI Signals (individual triggers)
    if rsi < RSI_OVERSOLD:
        buy_signals += 1
        individual_signals.append(("RSI_OVERSOLD", "BUY", 85))
    elif rsi > RSI_OVERBOUGHT:
        sell_signals += 1
        individual_signals.append(("RSI_OVERBOUGHT", "SELL", 85))
    elif rsi < 45:
        buy_signals += 0.7
        individual_signals.append(("RSI_LOW", "BUY", 70))
    elif rsi > 55:
        sell_signals += 0.7
        individual_signals.append(("RSI_HIGH", "SELL", 70))
    elif rsi < 50:
        buy_signals += 0.3
        individual_signals.append(("RSI_BEARISH", "BUY", 55))
    else:
        sell_signals += 0.3
        individual_signals.append(("RSI_BULLISH", "SELL", 55))
    
    # 2. MACD Signals
    macd_line = macd_data['macd'].iloc[-1]
    signal_line = macd_data['signal'].iloc[-1]
    prev_macd = macd_data['macd'].iloc[-2] if len(macd_data['macd']) > 1 else macd_line
    prev_signal = macd_data['signal'].iloc[-2] if len(macd_data['signal']) > 1 else signal_line
    
    # MACD crossover (individual triggers)
    if macd_line > signal_line and prev_macd <= prev_signal:
        buy_signals += 1
        individual_signals.append(("MACD_CROSS_UP", "BUY", 80))
    elif macd_line < signal_line and prev_macd >= prev_signal:
        sell_signals += 1
        individual_signals.append(("MACD_CROSS_DOWN", "SELL", 80))
    elif macd_line > signal_line:
        buy_signals += 0.5
        individual_signals.append(("MACD_ABOVE", "BUY", 65))
    elif macd_line < signal_line:
        sell_signals += 0.5
        individual_signals.append(("MACD_BELOW", "SELL", 65))
    
    # 3. Bollinger Bands (individual triggers)
    bb_upper = bb_data['upper'].iloc[-1]
    bb_lower = bb_data['lower'].iloc[-1]
    bb_middle = bb_data['middle'].iloc[-1]

    if current_price <= bb_lower:
        buy_signals += 1
        individual_signals.append(("BB_OVERSOLD", "BUY", 85))
    elif current_price >= bb_upper:
        sell_signals += 1
        individual_signals.append(("BB_OVERBOUGHT", "SELL", 85))
    elif current_price < bb_middle:
        buy_signals += 0.3
        individual_signals.append(("BB_BELOW_MID", "BUY", 60))
    elif current_price > bb_middle:
        sell_signals += 0.3
        individual_signals.append(("BB_ABOVE_MID", "SELL", 60))
    
    # 4. EMA Trend
    ema_fast = indicators['ema_fast'].iloc[-1]
    ema_slow = indicators['ema_slow'].iloc[-1]
    
    if ema_fast > ema_slow:
        buy_signals += 1
    else:
        sell_signals += 1
    
    # 5. Support/Resistance
    distance_to_support = abs(current_price - support_level)
    distance_to_resistance = abs(current_price - resistance_level)
    
    if distance_to_support < distance_to_resistance:
        buy_signals += 1
    else:
        sell_signals += 1
    
    # 6. Overall trend strength (simplified)
    if "BULLISH" in trend_strength:
        buy_signals += 1
    elif "BEARISH" in trend_strength:
        sell_signals += 1
    elif trend_strength == "NEUTRAL":
        # In neutral market, follow price momentum
        if current_price > indicators['previous_price']:
            buy_signals += 0.5
        else:
            sell_signals += 0.5

    # 7. Price momentum (additional signal)
    price_change = current_price - indicators['previous_price']
    if price_change > 0:
        buy_signals += 0.5  # Positive momentum
    elif price_change < 0:
        sell_signals += 0.5  # Negative momentum

    # 8. EMA alignment (additional signal)
    ema_fast = indicators['ema_fast'].iloc[-1]
    ema_slow = indicators['ema_slow'].iloc[-1]
    ema_trend = indicators['ema_trend'].iloc[-1]

    if ema_fast > ema_slow > ema_trend:  # Perfect bullish alignment
        buy_signals += 0.5
    elif ema_fast < ema_slow < ema_trend:  # Perfect bearish alignment
        sell_signals += 0.5
    
    # Calculate confidence
    buy_confidence = (buy_signals / max_signals) * 100
    sell_confidence = (sell_signals / max_signals) * 100
    
    # Decision logic
    min_confidence = 35  # Minimum 35% confidence required (ultra aggressive like your friend's bot)
    
    # Enhanced decision logic with medium confidence trades
    high_confidence = 65  # High confidence threshold

    if buy_confidence >= high_confidence and buy_confidence > sell_confidence:
        return "BUY", buy_confidence
    elif sell_confidence >= high_confidence and sell_confidence > buy_confidence:
        return "SELL", sell_confidence
    elif buy_confidence >= min_confidence and buy_confidence > sell_confidence:
        return "BUY", buy_confidence  # Medium confidence BUY
    elif sell_confidence >= min_confidence and sell_confidence > buy_confidence:
        return "SELL", sell_confidence  # Medium confidence SELL
    else:
        return "NO_SIGNAL", max(buy_confidence, sell_confidence)


def get_individual_signals(indicators, current_price, support_level, resistance_level, trend_strength):
    """
    Get individual trading signals that can trigger immediate trades
    Returns list of (rule_name, signal, confidence) tuples
    """
    signals = []

    # RSI signals
    rsi = indicators['rsi'].iloc[-1]
    if rsi < RSI_OVERSOLD:
        signals.append(("RSI_OVERSOLD", "BUY", 85))
    elif rsi > RSI_OVERBOUGHT:
        signals.append(("RSI_OVERBOUGHT", "SELL", 85))
    elif rsi < 35:
        signals.append(("RSI_VERY_LOW", "BUY", 75))
    elif rsi > 65:
        signals.append(("RSI_VERY_HIGH", "SELL", 75))

    # MACD signals
    macd_data = indicators['macd_data']
    macd_line = macd_data['macd'].iloc[-1]
    signal_line = macd_data['signal'].iloc[-1]
    prev_macd = macd_data['macd'].iloc[-2] if len(macd_data['macd']) > 1 else macd_line
    prev_signal = macd_data['signal'].iloc[-2] if len(macd_data['signal']) > 1 else signal_line

    if macd_line > signal_line and prev_macd <= prev_signal:
        signals.append(("MACD_BULLISH_CROSS", "BUY", 80))
    elif macd_line < signal_line and prev_macd >= prev_signal:
        signals.append(("MACD_BEARISH_CROSS", "SELL", 80))

    # Bollinger Bands signals
    bb_data = indicators['bb_data']
    bb_upper = bb_data['upper'].iloc[-1]
    bb_lower = bb_data['lower'].iloc[-1]

    if current_price <= bb_lower:
        signals.append(("BB_BOUNCE_BUY", "BUY", 85))
    elif current_price >= bb_upper:
        signals.append(("BB_BOUNCE_SELL", "SELL", 85))

    # EMA trend signals
    ema_fast = indicators['ema_fast'].iloc[-1]
    ema_slow = indicators['ema_slow'].iloc[-1]
    ema_trend = indicators['ema_trend'].iloc[-1]

    if ema_fast > ema_slow > ema_trend:
        signals.append(("EMA_PERFECT_BULL", "BUY", 90))
    elif ema_fast < ema_slow < ema_trend:
        signals.append(("EMA_PERFECT_BEAR", "SELL", 90))
    elif ema_fast > ema_slow:
        signals.append(("EMA_BULLISH", "BUY", 70))
    elif ema_fast < ema_slow:
        signals.append(("EMA_BEARISH", "SELL", 70))

    # PROFITABLE Support/Resistance signals (very precise entries only)
    distance_to_support = abs(current_price - support_level) / current_price * 100
    distance_to_resistance = abs(current_price - resistance_level) / current_price * 100

    # Only very precise entries for higher success rate
    if distance_to_support < 0.02:  # Very precise support bounce
        signals.append(("SUPPORT_BOUNCE", "BUY", 95))
    elif distance_to_resistance < 0.02:  # Very precise resistance rejection
        signals.append(("RESISTANCE_REJECT", "SELL", 95))

    # Trend strength signals
    if trend_strength == "STRONG_BULLISH":
        signals.append(("STRONG_BULL_TREND", "BUY", 75))
    elif trend_strength == "STRONG_BEARISH":
        signals.append(("STRONG_BEAR_TREND", "SELL", 75))

    # Enhanced price momentum signals with higher profit potential
    if 'previous_price' in indicators:
        price_change_pct = ((current_price - indicators['previous_price']) / indicators['previous_price']) * 100
        if price_change_pct > 0.1:  # Very strong upward momentum
            signals.append(("STRONG_MOMENTUM_UP", "BUY", 85))
        elif price_change_pct > 0.05:  # Strong upward momentum
            signals.append(("MOMENTUM_UP", "BUY", 75))
        elif price_change_pct > 0.02:  # Moderate upward momentum
            signals.append(("WEAK_MOMENTUM_UP", "BUY", 65))
        elif price_change_pct < -0.05:  # Strong downward momentum
            signals.append(("MOMENTUM_DOWN", "SELL", 75))
        elif price_change_pct < -0.02:  # Moderate downward momentum
            signals.append(("WEAK_MOMENTUM_DOWN", "SELL", 65))

    # Add profitable breakout and reversal signals
    if 'bb_data' in indicators:
        bb_upper = bb_data['upper'].iloc[-1]
        bb_lower = bb_data['lower'].iloc[-1]
        bb_width = (bb_upper - bb_lower) / current_price * 100

        # Tight bands breakout (high probability)
        if bb_width < 0.3:  # Very tight bands = strong breakout potential
            if current_price > bb_upper:
                signals.append(("BREAKOUT_UP", "BUY", 95))
            elif current_price < bb_lower:
                signals.append(("BREAKOUT_DOWN", "SELL", 95))

    # Add multi-timeframe confirmation signals
    rsi = indicators['rsi'].iloc[-1]
    macd_data = indicators['macd_data']
    macd_line = macd_data['macd'].iloc[-1]
    signal_line = macd_data['signal'].iloc[-1]

    # Triple confirmation signals (RSI + MACD + Trend)
    if (rsi < 30 and macd_line > signal_line and trend_strength in ["STRONG_BULLISH", "BULLISH"]):
        signals.append(("TRIPLE_BULL_CONFIRM", "BUY", 95))
    elif (rsi > 70 and macd_line < signal_line and trend_strength in ["STRONG_BEARISH", "BEARISH"]):
        signals.append(("TRIPLE_BEAR_CONFIRM", "SELL", 95))

    return signals


def print_indicator_summary(indicators):
    """
    Print a summary of all indicators for debugging
    """
    if not indicators:
        print("No indicators available")
        return
    
    print("\n📊 Technical Indicators Summary:")
    print("-" * 40)
    
    # Price and EMAs
    current_price = indicators['current_price']
    ema_fast = indicators['ema_fast'].iloc[-1]
    ema_slow = indicators['ema_slow'].iloc[-1]
    ema_trend = indicators['ema_trend'].iloc[-1]
    
    print(f"Price: {current_price:.2f}")
    print(f"EMA Fast ({EMA_FAST}): {ema_fast:.2f}")
    print(f"EMA Slow ({EMA_SLOW}): {ema_slow:.2f}")
    print(f"EMA Trend ({EMA_TREND}): {ema_trend:.2f}")
    
    # RSI
    rsi = indicators['rsi'].iloc[-1]
    print(f"RSI: {rsi:.1f}")
    
    # MACD
    macd_data = indicators['macd_data']
    print(f"MACD: {macd_data['macd'].iloc[-1]:.4f}")
    print(f"Signal: {macd_data['signal'].iloc[-1]:.4f}")
    print(f"Histogram: {macd_data['histogram'].iloc[-1]:.4f}")
    
    # Bollinger Bands
    bb_data = indicators['bb_data']
    print(f"BB Upper: {bb_data['upper'].iloc[-1]:.2f}")
    print(f"BB Middle: {bb_data['middle'].iloc[-1]:.2f}")
    print(f"BB Lower: {bb_data['lower'].iloc[-1]:.2f}")
    
    # Trend analysis
    trend = analyze_trend_strength(indicators)
    print(f"Trend Strength: {trend}")
    
    print("-" * 40)
